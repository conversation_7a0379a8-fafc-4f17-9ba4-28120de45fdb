#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化自动化任务引擎
功能：替代MaaFramework的任务执行功能，使用OpenCV+ADB实现，保持pipeline.json兼容性
作者：AI Assistant
日期：2025-08-05
"""

import json
import time
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable
from opencv_recognition import OpenCVRecognition
from android_controller import AndroidController

class SimpleAutomationEngine:
    """简化自动化任务引擎 - 新手友好的任务执行器"""
    
    def __init__(self, resource_path: str = "resource", device_address: str = "127.0.0.1:5555"):
        """
        初始化自动化引擎
        
        Args:
            resource_path: 资源文件夹路径
            device_address: Android设备地址
        """
        self.resource_path = Path(resource_path)
        self.device_address = device_address
        
        # 初始化日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.recognizer = OpenCVRecognition(str(self.resource_path / "image"))
        self.controller = AndroidController(device_address)
        
        # 加载配置
        self.pipeline_config = self._load_pipeline_config()
        
        # 执行状态
        self.current_task = None
        self.task_status = {}
        self.is_running = False
        
        # 执行参数
        self.default_wait_time = 1.0  # 默认等待时间（秒）
        self.max_retry_times = 3      # 最大重试次数
        self.screenshot_interval = 0.5  # 截图间隔
        
        self.logger.info("简化自动化引擎初始化完成")
    
    def _load_pipeline_config(self) -> Dict[str, Any]:
        """
        加载pipeline.json配置文件
        
        Returns:
            配置字典
        """
        pipeline_file = self.resource_path / "pipeline" / "pipeline.json"
        try:
            if pipeline_file.exists():
                with open(pipeline_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.logger.info(f"加载pipeline配置成功，包含 {len(config)} 个任务")
                return config
            else:
                self.logger.error(f"Pipeline配置文件不存在: {pipeline_file}")
                return {}
        except Exception as e:
            self.logger.error(f"加载pipeline配置失败: {e}")
            return {}
    
    def connect_device(self) -> bool:
        """
        连接Android设备
        
        Returns:
            连接是否成功
        """
        self.logger.info("正在连接Android设备...")
        success = self.controller.connect()
        if success:
            self.logger.info("设备连接成功，可以开始执行任务")
        else:
            self.logger.error("设备连接失败，请检查设备状态")
        return success
    
    def get_task_sequence(self, start_task: str = None) -> List[str]:
        """
        获取任务执行序列
        
        Args:
            start_task: 起始任务名称
            
        Returns:
            任务序列列表
        """
        if not self.pipeline_config:
            return []
        
        # 如果没有指定起始任务，找到第一个任务
        if start_task is None:
            all_tasks = set(self.pipeline_config.keys())
            referenced_tasks = set()
            
            # 找出被其他任务引用的任务
            for task_config in self.pipeline_config.values():
                if 'next' in task_config:
                    referenced_tasks.update(task_config['next'])
            
            # 第一个任务是没有被引用的任务
            start_tasks = all_tasks - referenced_tasks
            if start_tasks:
                start_task = list(start_tasks)[0]
            else:
                start_task = list(all_tasks)[0]
        
        # 构建任务序列
        sequence = []
        current_task = start_task
        visited = set()
        
        while current_task and current_task not in visited:
            if current_task in self.pipeline_config:
                sequence.append(current_task)
                visited.add(current_task)
                
                # 获取下一个任务
                task_config = self.pipeline_config[current_task]
                if 'next' in task_config and task_config['next']:
                    current_task = task_config['next'][0]  # 取第一个下一步任务
                else:
                    break
            else:
                self.logger.warning(f"任务不存在: {current_task}")
                break
        
        self.logger.info(f"生成任务序列: {' -> '.join(sequence)}")
        return sequence
    
    def execute_task_sequence(self, start_task: str = None, 
                            callback: Callable = None) -> bool:
        """
        执行任务序列
        
        Args:
            start_task: 起始任务名称
            callback: 状态回调函数 callback(task_name, status, current, total)
            
        Returns:
            执行是否成功
        """
        if not self.controller.is_connected:
            self.logger.error("设备未连接，无法执行任务")
            return False
        
        sequence = self.get_task_sequence(start_task)
        if not sequence:
            self.logger.error("无法获取任务序列")
            return False
        
        self.is_running = True
        self.logger.info(f"开始执行任务序列，共 {len(sequence)} 个任务")
        
        try:
            for i, task_name in enumerate(sequence):
                if not self.is_running:
                    self.logger.info("任务执行被中断")
                    return False
                
                self.current_task = task_name
                self.logger.info(f"执行任务 [{i+1}/{len(sequence)}]: {task_name}")
                
                # 更新状态
                self.task_status[task_name] = "执行中"
                if callback:
                    callback(task_name, "执行中", i+1, len(sequence))
                
                # 执行单个任务
                success = self._execute_single_task(task_name)
                
                if success:
                    self.task_status[task_name] = "完成"
                    if callback:
                        callback(task_name, "完成", i+1, len(sequence))
                    self.logger.info(f"任务完成: {task_name}")
                else:
                    self.task_status[task_name] = "失败"
                    if callback:
                        callback(task_name, "失败", i+1, len(sequence))
                    self.logger.error(f"任务失败: {task_name}")
                    return False
                
                # 任务间等待
                time.sleep(self.default_wait_time)
            
            self.logger.info("所有任务执行完成")
            return True
            
        except Exception as e:
            self.logger.error(f"任务序列执行异常: {e}")
            return False
        finally:
            self.is_running = False
            self.current_task = None
    
    def _execute_single_task(self, task_name: str) -> bool:
        """
        执行单个任务
        
        Args:
            task_name: 任务名称
            
        Returns:
            执行是否成功
        """
        task_config = self.pipeline_config.get(task_name)
        if not task_config:
            self.logger.error(f"任务配置不存在: {task_name}")
            return False
        
        action = task_config.get('action')
        if not action:
            self.logger.error(f"任务缺少action字段: {task_name}")
            return False
        
        self.logger.debug(f"执行动作: {action}")
        
        # 处理等待时间
        if 'pre_wait_freezes' in task_config:
            wait_time = task_config['pre_wait_freezes'].get('time', 0) / 1000.0
            self.logger.debug(f"任务前等待: {wait_time} 秒")
            time.sleep(wait_time)
        
        # 根据动作类型执行相应操作
        if action == "StartApp":
            return self._execute_start_app(task_config)
        elif action == "Click":
            return self._execute_click(task_config)
        elif action == "InputText":
            return self._execute_input_text(task_config)
        elif action == "ReadEmailFromFile":
            return self._execute_read_email_from_file(task_config)
        else:
            self.logger.error(f"不支持的动作类型: {action}")
            return False
    
    def _execute_start_app(self, task_config: Dict[str, Any]) -> bool:
        """执行启动应用动作"""
        package = task_config.get('package')
        if not package:
            self.logger.error("StartApp动作缺少package参数")
            return False
        
        return self.controller.start_app(package)
    
    def _execute_click(self, task_config: Dict[str, Any]) -> bool:
        """执行点击动作"""
        recognition = task_config.get('recognition')
        if not recognition:
            self.logger.error("Click动作缺少recognition参数")
            return False
        
        # 获取设备截图
        screenshot_data = self.controller.get_screenshot()
        if not screenshot_data:
            self.logger.error("无法获取设备截图")
            return False
        
        # 转换截图为OpenCV格式
        import numpy as np
        import cv2
        nparr = np.frombuffer(screenshot_data, np.uint8)
        screenshot = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if screenshot is None:
            self.logger.error("截图解码失败")
            return False
        
        # 根据识别类型查找元素
        position = None
        if recognition == "TemplateMatch":
            template = task_config.get('template')
            if template:
                position = self.recognizer.find_element(
                    screenshot, "TemplateMatch", template=template
                )
        elif recognition == "OCR":
            expected = task_config.get('expected')
            if expected:
                position = self.recognizer.find_element(
                    screenshot, "OCR", expected=expected
                )
        
        if position:
            x, y = position
            # 保存调试图片
            self.recognizer.save_debug_image(
                screenshot, f"click_{task_config.get('template', 'ocr')}_{int(time.time())}.png", 
                (x, y)
            )
            return self.controller.click(x, y)
        else:
            self.logger.error(f"未找到点击目标: {task_config}")
            # 保存失败的截图用于调试
            self.recognizer.save_debug_image(
                screenshot, f"click_failed_{int(time.time())}.png"
            )
            return False
    
    def _execute_input_text(self, task_config: Dict[str, Any]) -> bool:
        """执行文本输入动作"""
        input_text = task_config.get('input_text', '')
        if not input_text:
            self.logger.warning("InputText动作的input_text为空")
            return True  # 空文本也算成功
        
        return self.controller.input_text(input_text)
    
    def _execute_read_email_from_file(self, task_config: Dict[str, Any]) -> bool:
        """执行从文件读取邮箱动作（自定义动作）"""
        file_name = task_config.get('file_name', '邮箱.txt')
        line_number = task_config.get('line_number', 1)
        
        # 读取邮箱文件
        email_file = self.resource_path / "pipeline" / file_name
        try:
            if email_file.exists():
                with open(email_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if line_number <= len(lines):
                        email = lines[line_number - 1].strip()
                        self.logger.info(f"读取邮箱地址: {email}")
                        
                        # 输入邮箱地址
                        return self.controller.input_text(email)
                    else:
                        self.logger.error(f"行号超出范围: {line_number}")
                        return False
            else:
                self.logger.error(f"邮箱文件不存在: {email_file}")
                return False
        except Exception as e:
            self.logger.error(f"读取邮箱文件失败: {e}")
            return False
    
    def stop_execution(self):
        """停止任务执行"""
        self.is_running = False
        self.logger.info("任务执行已停止")
    
    def get_execution_status(self) -> Dict[str, Any]:
        """
        获取执行状态
        
        Returns:
            状态信息字典
        """
        return {
            'is_running': self.is_running,
            'current_task': self.current_task,
            'task_status': self.task_status.copy(),
            'device_connected': self.controller.is_connected
        }
    
    def validate_configuration(self) -> List[str]:
        """
        验证配置
        
        Returns:
            错误信息列表
        """
        errors = []
        
        # 检查pipeline配置
        if not self.pipeline_config:
            errors.append("Pipeline配置为空")
            return errors
        
        # 检查每个任务的配置
        for task_name, task_config in self.pipeline_config.items():
            if 'action' not in task_config:
                errors.append(f"任务 '{task_name}' 缺少action字段")
            
            action = task_config.get('action')
            
            # 检查StartApp动作
            if action == "StartApp" and 'package' not in task_config:
                errors.append(f"任务 '{task_name}' 的StartApp动作缺少package参数")
            
            # 检查Click动作
            if action == "Click":
                if 'recognition' not in task_config:
                    errors.append(f"任务 '{task_name}' 的Click动作缺少recognition参数")
                else:
                    recognition = task_config['recognition']
                    if recognition == "TemplateMatch" and 'template' not in task_config:
                        errors.append(f"任务 '{task_name}' 的TemplateMatch缺少template参数")
                    elif recognition == "OCR" and 'expected' not in task_config:
                        errors.append(f"任务 '{task_name}' 的OCR缺少expected参数")
            
            # 检查模板图片是否存在
            if task_config.get('recognition') == 'TemplateMatch':
                template = task_config.get('template')
                if template:
                    template_path = self.resource_path / "image" / template
                    if not template_path.exists():
                        errors.append(f"任务 '{task_name}' 的模板图片不存在: {template}")
        
        if errors:
            self.logger.warning(f"配置验证发现 {len(errors)} 个问题")
        else:
            self.logger.info("配置验证通过")
        
        return errors
    
    def disconnect_device(self):
        """断开设备连接"""
        self.controller.disconnect()


# 使用示例和测试
if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建自动化引擎
    engine = SimpleAutomationEngine()
    
    print("简化自动化任务引擎测试")
    print("=" * 40)
    
    # 验证配置
    errors = engine.validate_configuration()
    if errors:
        print("配置验证错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✓ 配置验证通过")
    
    # 获取任务序列
    sequence = engine.get_task_sequence()
    print(f"任务序列: {' -> '.join(sequence)}")
    
    # 测试设备连接
    if engine.connect_device():
        print("✓ 设备连接成功")
        
        # 定义状态回调
        def status_callback(task_name, status, current, total):
            print(f"[{current}/{total}] {task_name}: {status}")
        
        # 询问是否执行任务
        choice = input("\n是否执行任务序列? (y/n): ").lower()
        if choice == 'y':
            success = engine.execute_task_sequence(callback=status_callback)
            print(f"执行结果: {'成功' if success else '失败'}")
        
        engine.disconnect_device()
    else:
        print("✗ 设备连接失败")
    
    print("\n引擎测试完成")
